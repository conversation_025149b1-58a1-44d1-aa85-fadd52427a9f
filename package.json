{"name": "emoji-extension", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "node scripts/build.js dev", "dev:variant": "node scripts/build.js dev:variant", "watch": "node scripts/watch.js", "build": "node scripts/build.js build", "build:variant": "node scripts/build.js build:variant", "build:prod": "node scripts/build.js build:prod", "build:no-indexeddb": "node scripts/build.js build:no-indexeddb", "build:minimal": "node scripts/build.js build:minimal", "serve": "vite preview", "test": "playwright test", "test:debug": "playwright test --debug", "release": "bash ./scripts/release.sh", "pack:crx": "node ./scripts/pack-crx.js"}, "dependencies": {"ant-design-vue": "^4.1.2", "pinia": "^2.1.7", "vue": "^3.4.21"}, "devDependencies": {"@ant-design/icons-vue": "^7.0.1", "@playwright/test": "^1.55.0", "@tailwindcss/typography": "^0.5.16", "@types/chrome": "^0.1.4", "@types/node": "^24.3.0", "@vitejs/plugin-vue": "^6.0.1", "@vue/devtools": "^8.0.0", "autoprefixer": "^10.4.21", "crx": "^5.0.1", "less": "^4.4.1", "playwright": "^1.55.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "terser": "^5.43.1", "typescript": "^5.3.3", "unplugin-auto-import": "^0.17.5", "unplugin-vue-components": "^0.26.0", "vite": "^6.0.0"}, "dependenciesMeta": {}, "devDependenciesExtra": {}, "devDependenciesAdditions": {}, "devDependencies2": {}, "peerDependenciesMeta": {}, "bundledDependencies": [], "overrides": {}, "engines": {}}