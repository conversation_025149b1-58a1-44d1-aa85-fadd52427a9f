<!DOCTYPE html>
<html>
<head>
    <title>存储调试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .button:hover { background: #005a87; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; white-space: pre-wrap; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>存储系统调试页面</h1>
    
    <div class="section">
        <h2>存储检查</h2>
        <button class="button" onclick="checkStorage()">检查所有存储数据</button>
        <button class="button" onclick="checkNewStorage()">检查新存储系统</button>
        <button class="button" onclick="initializeDefaults()">初始化默认数据</button>
        <div id="storageResult"></div>
    </div>

    <div class="section">
        <h2>内容脚本测试</h2>
        <button class="button" onclick="testContentScript()">测试内容脚本数据加载</button>
        <div id="contentResult"></div>
    </div>

    <script>
        async function checkStorage() {
            const result = document.getElementById('storageResult');
            result.innerHTML = '<div>正在检查存储...</div>';
            
            try {
                // Check chrome storage
                const chromeData = await new Promise((resolve) => {
                    chrome.storage.local.get(null, resolve);
                });
                
                // Check localStorage
                const localData = {};
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    try {
                        localData[key] = JSON.parse(localStorage.getItem(key));
                    } catch (e) {
                        localData[key] = localStorage.getItem(key);
                    }
                }

                result.innerHTML = `
                    <div class="success">存储检查完成</div>
                    <h3>Chrome Extension Storage:</h3>
                    <pre>${JSON.stringify(chromeData, null, 2)}</pre>
                    <h3>Local Storage:</h3>
                    <pre>${JSON.stringify(localData, null, 2)}</pre>
                `;
            } catch (error) {
                result.innerHTML = `<div class="error">检查失败: ${error.message}</div>`;
            }
        }

        async function checkNewStorage() {
            const result = document.getElementById('storageResult');
            result.innerHTML = '<div>正在检查新存储系统...</div>';
            
            try {
                // Check new storage system keys
                const keys = ['emojiGroupIndex', 'appSettings', 'favorites'];
                const data = {};
                
                for (const key of keys) {
                    const value = await new Promise((resolve) => {
                        chrome.storage.local.get({[key]: null}, (result) => resolve(result[key]));
                    });
                    data[key] = value;
                }
                
                // Check for individual groups
                const groupIndex = data['emojiGroupIndex'];
                if (groupIndex && groupIndex.data && Array.isArray(groupIndex.data)) {
                    for (const groupInfo of groupIndex.data) {
                        const groupKey = `emojiGroup_${groupInfo.id}`;
                        const groupData = await new Promise((resolve) => {
                            chrome.storage.local.get({[groupKey]: null}, (result) => resolve(result[groupKey]));
                        });
                        data[groupKey] = groupData;
                    }
                }

                result.innerHTML = `
                    <div class="success">新存储系统检查完成</div>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                result.innerHTML = `<div class="error">检查失败: ${error.message}</div>`;
            }
        }

        async function initializeDefaults() {
            const result = document.getElementById('storageResult');
            result.innerHTML = '<div>正在初始化默认数据...</div>';
            
            try {
                // Create default emoji group
                const defaultGroup = {
                    id: 'nachoneko',
                    name: '纳小猫',
                    icon: '🐈‍⬛',
                    order: 0,
                    emojis: [
                        {
                            id: 'emoji-1',
                            name: '瞌睡',
                            url: 'https://linux.do/uploads/default/optimized/4X/5/9/f/59ffbc2c53dd2a07dc30d4368bd5c9e01ca57d80_2_490x500.jpeg',
                            groupId: 'nachoneko'
                        },
                        {
                            id: 'emoji-2',
                            name: '哭泣',
                            url: 'https://linux.do/uploads/default/optimized/4X/5/d/9/5d932c05a642396335f632a370bd8d45463cf2e2_2_503x500.jpeg',
                            groupId: 'nachoneko'
                        }
                    ]
                };

                const defaultSettings = {
                    imageScale: 30,
                    gridColumns: 4,
                    defaultGroup: 'nachoneko',
                    lastModified: Date.now()
                };

                const groupIndex = [{ id: 'nachoneko', order: 0 }];

                // Save using new storage format
                const timestamp = Date.now();
                await Promise.all([
                    new Promise((resolve) => {
                        chrome.storage.local.set({
                            'emojiGroupIndex': { data: groupIndex, timestamp }
                        }, resolve);
                    }),
                    new Promise((resolve) => {
                        chrome.storage.local.set({
                            'emojiGroup_nachoneko': { data: defaultGroup, timestamp }
                        }, resolve);
                    }),
                    new Promise((resolve) => {
                        chrome.storage.local.set({
                            'appSettings': { data: defaultSettings, timestamp }
                        }, resolve);
                    }),
                    new Promise((resolve) => {
                        chrome.storage.local.set({
                            'favorites': { data: [], timestamp }
                        }, resolve);
                    })
                ]);

                result.innerHTML = `
                    <div class="success">默认数据初始化完成</div>
                    <p>已创建默认表情组 "${defaultGroup.name}" 包含 ${defaultGroup.emojis.length} 个表情</p>
                `;
            } catch (error) {
                result.innerHTML = `<div class="error">初始化失败: ${error.message}</div>`;
            }
        }

        async function testContentScript() {
            const result = document.getElementById('contentResult');
            result.innerHTML = '<div>正在测试内容脚本数据加载...</div>';
            
            try {
                // This simulates what the content script does
                class TestContentStorageAdapter {
                    async get(key) {
                        if (chrome?.storage?.local) {
                            try {
                                const result = await new Promise((resolve) => {
                                    chrome.storage.local.get({[key]: null}, resolve);
                                });
                                const value = result[key];
                                if (value !== null && value !== undefined) {
                                    console.log(`[Test] Raw value for ${key}:`, value);
                                    // Handle both new storage format (with .data) and legacy format
                                    if (value && typeof value === 'object' && value.data !== undefined) {
                                        console.log(`[Test] Returning .data for ${key}:`, value.data);
                                        return value.data;
                                    }
                                    console.log(`[Test] Returning direct value for ${key}:`, value);
                                    return value;
                                }
                            } catch (error) {
                                console.warn(`Extension storage failed for ${key}:`, error);
                            }
                        }
                        return null;
                    }

                    async getAllEmojiGroups() {
                        console.log('[Test] Getting all emoji groups');
                        
                        // First try to get the group index
                        const groupIndex = await this.get('emojiGroupIndex');
                        console.log('[Test] Group index:', groupIndex);
                        
                        if (groupIndex && Array.isArray(groupIndex) && groupIndex.length > 0) {
                            const groups = [];
                            for (const groupInfo of groupIndex) {
                                console.log(`[Test] Processing group info:`, groupInfo);
                                if (groupInfo && groupInfo.id) {
                                    const group = await this.get(`emojiGroup_${groupInfo.id}`);
                                    console.log(`[Test] Raw group data for ${groupInfo.id}:`, group);
                                    
                                    if (group) {
                                        console.log(`[Test] Group structure:`, {
                                            hasEmojis: !!group.emojis,
                                            emojisType: typeof group.emojis,
                                            isArray: Array.isArray(group.emojis),
                                            emojisLength: group.emojis?.length,
                                            groupKeys: Object.keys(group)
                                        });
                                        
                                        // Handle case where emojis is stored as an object instead of array
                                        let emojisArray = group.emojis;
                                        if (group.emojis && typeof group.emojis === 'object' && !Array.isArray(group.emojis)) {
                                            // Convert object to array if needed
                                            emojisArray = Object.values(group.emojis);
                                            console.log(`[Test] Converting emojis object to array for ${group.name}, length: ${emojisArray.length}`);
                                        }
                                        
                                        if (emojisArray && Array.isArray(emojisArray)) {
                                            const processedGroup = { ...group, emojis: emojisArray, order: groupInfo.order || 0 };
                                            groups.push(processedGroup);
                                            console.log(`[Test] ✅ Loaded group: ${group.name} with ${emojisArray.length} emojis`);
                                        } else if (groupInfo.id === 'favorites') {
                                            // Special handling for favorites group
                                            const favoritesGroup = { 
                                                ...group, 
                                                emojis: emojisArray && Array.isArray(emojisArray) ? emojisArray : [], 
                                                order: groupInfo.order || 0 
                                            };
                                            groups.push(favoritesGroup);
                                            console.log(`[Test] ✅ Loaded favorites group with ${favoritesGroup.emojis.length} emojis`);
                                        } else {
                                            console.warn(`[Test] ❌ Group ${group.name || groupInfo.id} has invalid emojis after conversion:`, {
                                                hasEmojis: !!emojisArray,
                                                emojisType: typeof emojisArray,
                                                isArray: Array.isArray(emojisArray),
                                                originalEmojisType: typeof group.emojis
                                            });
                                        }
                                    } else {
                                        console.warn(`[Test] ❌ Group ${groupInfo.id} data is null/undefined`);
                                    }
                                }
                            }
                            
                            console.log(`[Test] Processed ${groupIndex.length} groups, ${groups.length} valid groups found`);
                            
                            if (groups.length > 0) {
                                console.log(`[Test] Successfully loaded ${groups.length} groups from new storage system`);
                                return groups.sort((a, b) => a.order - b.order);
                            } else {
                                console.warn(`[Test] No valid groups found in new storage system despite having group index`);
                            }
                        }

                        // Fallback to legacy emojiGroups key
                        console.log('[Test] Trying legacy emojiGroups key');
                        const legacyGroups = await this.get('emojiGroups');
                        if (legacyGroups && Array.isArray(legacyGroups) && legacyGroups.length > 0) {
                            console.log(`[Test] Loaded ${legacyGroups.length} groups from legacy storage`);
                            return legacyGroups;
                        }

                        console.log('[Test] No groups found in storage');
                        return [];
                    }

                    async getSettings() {
                        console.log('[Test] Getting settings');
                        const settings = await this.get('appSettings');
                        const defaultSettings = { imageScale: 30, gridColumns: 4 };
                        const result = settings ? { ...defaultSettings, ...settings } : defaultSettings;
                        console.log('[Test] Settings loaded:', result);
                        return result;
                    }
                }

                const adapter = new TestContentStorageAdapter();
                const groups = await adapter.getAllEmojiGroups();
                const settings = await adapter.getSettings();

                let totalEmojis = 0;
                const groupSummary = groups.map(group => {
                    const emojiCount = group?.emojis?.length || 0;
                    totalEmojis += emojiCount;
                    return {
                        id: group.id,
                        name: group.name,
                        emojiCount: emojiCount,
                        hasValidEmojis: group.emojis && Array.isArray(group.emojis)
                    };
                });

                result.innerHTML = `
                    <div class="success">内容脚本测试完成</div>
                    <h3>加载的数据:</h3>
                    <p><strong>表情组数量:</strong> ${groups.length}</p>
                    <p><strong>总表情数量:</strong> ${totalEmojis}</p>
                    <p><strong>设置:</strong></p>
                    <pre>${JSON.stringify(settings, null, 2)}</pre>
                    <h3>表情组摘要:</h3>
                    <pre>${JSON.stringify(groupSummary, null, 2)}</pre>
                    <h3>详细的控制台日志:</h3>
                    <p style="color: #666;">请查看浏览器控制台以获取详细的调试信息</p>
                `;
            } catch (error) {
                result.innerHTML = `<div class="error">测试失败: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
