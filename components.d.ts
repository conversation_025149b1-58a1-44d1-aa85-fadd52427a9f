/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AboutSection: typeof import('./src/components/AboutSection.vue')['default']
    GridColumnsSelector: typeof import('./src/components/GridColumnsSelector.vue')['default']
  }
}
